{"info": {"name": "Employer API Tests", "description": "Collection for testing Employer module API endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_postman_id": "employer-api-collection", "version": "2.0.0"}, "variable": [{"key": "base_url", "value": "http://localhost/employer", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "phone", "value": "+998901234567", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Send SMS Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/send-code", "host": ["{{base_url}}"], "path": ["auth", "send-code"]}}}, {"name": "Verify Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone}}\",\n    \"code\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/auth/verify-code", "host": ["{{base_url}}"], "path": ["auth", "verify-code"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    var jsonData = pm.response.json();", "    if (jsonData.success && jsonData.data && jsonData.data.auth_token) {", "        pm.collectionVariables.set('auth_token', jsonData.data.auth_token);", "    }", "}"]}}]}, {"name": "Complete Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"business_name\": \"My Company LLC\",\n    \"business_inn\": \"*********\",\n    \"business_address\": \"Tashkent, Uzbekistan\"\n}"}, "url": {"raw": "{{base_url}}/auth/complete-registration", "host": ["{{base_url}}"], "path": ["auth", "complete-registration"]}}}, {"name": "Auth Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/auth/status", "host": ["{{base_url}}"], "path": ["auth", "status"]}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}}}]}, {"name": "Workers", "item": [{"name": "Public Worker List", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/worker/list?page=1&limit=10", "host": ["{{base_url}}"], "path": ["worker", "list"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Search Workers", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/worker/search?query=developer&page=1&limit=10", "host": ["{{base_url}}"], "path": ["worker", "search"], "query": [{"key": "query", "value": "developer"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Worker Detail (Requires Auth)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/worker/detail?id=1", "host": ["{{base_url}}"], "path": ["worker", "detail"], "query": [{"key": "id", "value": "1"}]}}}, {"name": "Get Professions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/worker/professions", "host": ["{{base_url}}"], "path": ["worker", "professions"]}}}, {"name": "Unlock Worker Contact", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/worker/unlock-contact?id=1", "host": ["{{base_url}}"], "path": ["worker", "unlock-contact"], "query": [{"key": "id", "value": "1"}]}}}, {"name": "Check Worker Access", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/worker/check-access?id=1", "host": ["{{base_url}}"], "path": ["worker", "check-access"], "query": [{"key": "id", "value": "1"}]}}}, {"name": "Workers Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/worker/statistics", "host": ["{{base_url}}"], "path": ["worker", "statistics"]}}}, {"name": "Workers by Profession", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/worker/by-profession?profession_id=1&page=1&limit=10", "host": ["{{base_url}}"], "path": ["worker", "by-profession"], "query": [{"key": "profession_id", "value": "1"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}]}, {"name": "Favorites", "item": [{"name": "Add to Favorites", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"worker_id\": 1\n}"}, "url": {"raw": "{{base_url}}/favorite/add", "host": ["{{base_url}}"], "path": ["favorite", "add"]}}}, {"name": "Remove from Favorites", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"worker_id\": 1\n}"}, "url": {"raw": "{{base_url}}/favorite/remove", "host": ["{{base_url}}"], "path": ["favorite", "remove"]}}}, {"name": "List Favorites", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/favorite/list?page=1&limit=10", "host": ["{{base_url}}"], "path": ["favorite", "list"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Toggle Favorite", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"worker_id\": 1\n}"}, "url": {"raw": "{{base_url}}/favorite/toggle", "host": ["{{base_url}}"], "path": ["favorite", "toggle"]}}}, {"name": "Favorites Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/favorite/statistics", "host": ["{{base_url}}"], "path": ["favorite", "statistics"]}}}, {"name": "Bulk Add to Favorites", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"worker_ids\": [1, 2, 3]\n}"}, "url": {"raw": "{{base_url}}/favorite/bulk-add", "host": ["{{base_url}}"], "path": ["favorite", "bulk-add"]}}}, {"name": "Bulk Remove from Favorites", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"worker_ids\": [1, 2, 3]\n}"}, "url": {"raw": "{{base_url}}/favorite/bulk-remove", "host": ["{{base_url}}"], "path": ["favorite", "bulk-remove"]}}}, {"name": "Favorites by <PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/favorite/by-profession?profession_id=1&limit=10", "host": ["{{base_url}}"], "path": ["favorite", "by-profession"], "query": [{"key": "profession_id", "value": "1"}, {"key": "limit", "value": "10"}]}}}]}, {"name": "Profile", "item": [{"name": "View Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/profile/view", "host": ["{{base_url}}"], "path": ["profile", "view"]}}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"business_name\": \"My Company LLC\",\n    \"business_inn\": \"*********\",\n    \"business_address\": \"Tashkent, Uzbekistan\"\n}"}, "url": {"raw": "{{base_url}}/profile/update", "host": ["{{base_url}}"], "path": ["profile", "update"]}}}, {"name": "Change Language", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"language\": \"ru\"\n}"}, "url": {"raw": "{{base_url}}/profile/change-language", "host": ["{{base_url}}"], "path": ["profile", "change-language"]}}}, {"name": "Get Available Languages", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/profile/languages", "host": ["{{base_url}}"], "path": ["profile", "languages"]}}}, {"name": "Delete Profile", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"confirm_deletion\": \"DELETE\"\n}"}, "url": {"raw": "{{base_url}}/profile/delete", "host": ["{{base_url}}"], "path": ["profile", "delete"]}}}]}, {"name": "Vacancies", "item": [{"name": "List Vacancies", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/vacancy/list?page=1&limit=10", "host": ["{{base_url}}"], "path": ["vacancy", "list"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "View Vacancy", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/vacancy/view?id=1", "host": ["{{base_url}}"], "path": ["vacancy", "view"], "query": [{"key": "id", "value": "1"}]}}}, {"name": "Create Vacancy", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Senior Developer\",\n    \"description\": \"Job description here\",\n    \"profession_id\": 1,\n    \"salary_from\": 1000,\n    \"salary_to\": 2000,\n    \"experience_from\": 3,\n    \"experience_to\": 5,\n    \"status\": \"active\",\n    \"location\": \"Tashkent\",\n    \"latitude\": 41.2995,\n    \"longitude\": 69.2401\n}"}, "url": {"raw": "{{base_url}}/vacancy/create", "host": ["{{base_url}}"], "path": ["vacancy", "create"]}}}, {"name": "Update Vacancy", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"id\": 1,\n    \"title\": \"Updated Senior Developer\",\n    \"status\": \"paused\"\n}"}, "url": {"raw": "{{base_url}}/vacancy/update", "host": ["{{base_url}}"], "path": ["vacancy", "update"]}}}, {"name": "Delete Vacancy", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"id\": 1\n}"}, "url": {"raw": "{{base_url}}/vacancy/delete", "host": ["{{base_url}}"], "path": ["vacancy", "delete"]}}}, {"name": "Get Vacancy Statuses", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/vacancy/statuses", "host": ["{{base_url}}"], "path": ["vacancy", "statuses"]}}}]}, {"name": "Access Control", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/access/filter-page?industry_id=1&profession_ids[]=1&profession_ids[]=2&experience_from=2&experience_to=5&age_from=20&age_to=35&latitude=41.2995&longitude=69.2401&radius=10&page=1&limit=20", "host": ["{{base_url}}"], "path": ["access", "filter-page"], "query": [{"key": "industry_id", "value": "1"}, {"key": "profession_ids[]", "value": "1"}, {"key": "profession_ids[]", "value": "2"}, {"key": "experience_from", "value": "2"}, {"key": "experience_to", "value": "5"}, {"key": "age_from", "value": "20"}, {"key": "age_to", "value": "35"}, {"key": "latitude", "value": "41.2995"}, {"key": "longitude", "value": "69.2401"}, {"key": "radius", "value": "10"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Calculate Price", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"worker_ids\": [1, 2, 3, 4, 5],\n    \"tariff_plan_id\": 1\n}"}, "url": {"raw": "{{base_url}}/access/calculate-price", "host": ["{{base_url}}"], "path": ["access", "calculate-price"]}}}, {"name": "Purchase Access", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"worker_ids\": [1, 2, 3],\n    \"tariff_plan_id\": 1,\n    \"payment_method\": \"click\",\n    \"return_url\": \"https://example.com/return\"\n}"}, "url": {"raw": "{{base_url}}/access/purchase", "host": ["{{base_url}}"], "path": ["access", "purchase"]}}}, {"name": "My Purchases", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/access/my-purchases", "host": ["{{base_url}}"], "path": ["access", "my-purchases"]}}}, {"name": "Available Workers", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/access/available?page=1&limit=20", "host": ["{{base_url}}"], "path": ["access", "available"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Unlock Contact", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/access/unlock-contact?worker_id=1", "host": ["{{base_url}}"], "path": ["access", "unlock-contact"], "query": [{"key": "worker_id", "value": "1"}]}}}, {"name": "Contact Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/access/contact-status?worker_id=1", "host": ["{{base_url}}"], "path": ["access", "contact-status"], "query": [{"key": "worker_id", "value": "1"}]}}}, {"name": "Access Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/access/statistics", "host": ["{{base_url}}"], "path": ["access", "statistics"]}}}]}, {"name": "Tariff Plans", "item": [{"name": "Get Tariff Plans", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/tariff/plans?worker_count=5&show_recommendations=true", "host": ["{{base_url}}"], "path": ["tariff", "plans"], "query": [{"key": "worker_count", "value": "5"}, {"key": "show_recommendations", "value": "true"}]}}}, {"name": "Get Tariff Detail", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/tariff/detail?tariff_id=1", "host": ["{{base_url}}"], "path": ["tariff", "detail"], "query": [{"key": "tariff_id", "value": "1"}]}}}, {"name": "Compare Tariffs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/tariff/compare?worker_ids[]=1&worker_ids[]=2&worker_ids[]=3", "host": ["{{base_url}}"], "path": ["tariff", "compare"], "query": [{"key": "worker_ids[]", "value": "1"}, {"key": "worker_ids[]", "value": "2"}, {"key": "worker_ids[]", "value": "3"}]}}}, {"name": "Get Recommended Tariff", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/tariff/recommended?worker_count=10", "host": ["{{base_url}}"], "path": ["tariff", "recommended"], "query": [{"key": "worker_count", "value": "10"}]}}}, {"name": "Get Tariff Discounts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/tariff/discounts?tariff_id=1&worker_count=25", "host": ["{{base_url}}"], "path": ["tariff", "discounts"], "query": [{"key": "tariff_id", "value": "1"}, {"key": "worker_count", "value": "25"}]}}}, {"name": "Filtered Tariffs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/tariff/filtered?is_active=true&min_duration=30&max_duration=90&min_price=3000&max_price=6000", "host": ["{{base_url}}"], "path": ["tariff", "filtered"], "query": [{"key": "is_active", "value": "true"}, {"key": "min_duration", "value": "30"}, {"key": "max_duration", "value": "90"}, {"key": "min_price", "value": "3000"}, {"key": "max_price", "value": "6000"}]}}}, {"name": "Tariff Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/tariff/statistics", "host": ["{{base_url}}"], "path": ["tariff", "statistics"]}}}]}, {"name": "Payment System", "item": [{"name": "Get Payment Methods", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/payment/methods", "host": ["{{base_url}}"], "path": ["payment", "methods"]}}}, {"name": "Get Payment Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/payment/status?payment_id=1", "host": ["{{base_url}}"], "path": ["payment", "status"], "query": [{"key": "payment_id", "value": "1"}]}}}, {"name": "Payment History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/payment/history?status=completed&page=1&limit=20", "host": ["{{base_url}}"], "path": ["payment", "history"], "query": [{"key": "status", "value": "completed"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Retry Payment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"payment_id\": 1,\n    \"return_url\": \"https://example.com/return\"\n}"}, "url": {"raw": "{{base_url}}/payment/retry", "host": ["{{base_url}}"], "path": ["payment", "retry"]}}}, {"name": "Payment Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/payment/statistics?date_from=2024-01-01&date_to=2024-12-31&method=click", "host": ["{{base_url}}"], "path": ["payment", "statistics"], "query": [{"key": "date_from", "value": "2024-01-01"}, {"key": "date_to", "value": "2024-12-31"}, {"key": "method", "value": "click"}]}}}, {"name": "Click Payment Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"click_trans_id\": \"*********\",\n    \"service_id\": \"12345\",\n    \"click_paydoc_id\": \"987654321\",\n    \"merchant_trans_id\": \"order_123\",\n    \"amount\": \"50000\",\n    \"action\": \"1\",\n    \"error\": \"0\",\n    \"error_note\": \"Success\",\n    \"sign_time\": \"2024-01-01 12:00:00\",\n    \"sign_string\": \"signature_hash\"\n}"}, "url": {"raw": "{{base_url}}/payment/click-callback", "host": ["{{base_url}}"], "path": ["payment", "click-callback"]}}}, {"name": "Payme Payment Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"jsonrpc\": \"2.0\",\n    \"id\": 123,\n    \"method\": \"CheckPerformTransaction\",\n    \"params\": {\n        \"amount\": 5000000,\n        \"account\": {\n            \"order_id\": \"order_123\"\n        }\n    }\n}"}, "url": {"raw": "{{base_url}}/payment/payme-callback", "host": ["{{base_url}}"], "path": ["payment", "payme-callback"]}}}, {"name": "UzCard Payment Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"order\": \"order_123\",\n    \"transaction_id\": \"uzcard_trans_456\",\n    \"status\": \"success\",\n    \"amount\": \"50000\",\n    \"signature\": \"signature_hash\"\n}"}, "url": {"raw": "{{base_url}}/payment/uzcard-callback", "host": ["{{base_url}}"], "path": ["payment", "uzcard-callback"]}}}]}]}